import { View, Text } from 'react-native'
import React from 'react'
import { Stack } from 'expo-router'
import { HeaderTitle } from '@react-navigation/elements'

const _layout = () => {
  return (
    <Stack screenOptions={{headerShown:false}}>
      
      <Stack.Screen name='(tab)'/>
      <Stack.Screen name='index'/>
      <Stack.Screen name='about' />
      
    </Stack>
  )
}

export default _layout