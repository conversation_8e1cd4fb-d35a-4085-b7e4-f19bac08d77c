/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tab)'}/home.tsx/about` | `/home.tsx/about`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tab)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tab)'}/about` | `/about`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tab)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/about`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tab)'}/home.tsx/about` | `/home.tsx/about`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tab)'}/home` | `/home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tab)'}/about` | `/about`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tab)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/about`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `${'/(tab)'}/home.tsx/about${`?${string}` | `#${string}` | ''}` | `/home.tsx/about${`?${string}` | `#${string}` | ''}` | `${'/(tab)'}/home${`?${string}` | `#${string}` | ''}` | `/home${`?${string}` | `#${string}` | ''}` | `${'/(tab)'}/about${`?${string}` | `#${string}` | ''}` | `/about${`?${string}` | `#${string}` | ''}` | `${'/(tab)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/about${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tab)'}/home.tsx/about` | `/home.tsx/about`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tab)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tab)'}/about` | `/about`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tab)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/about`; params?: Router.UnknownInputParams; };
    }
  }
}
